import { useEffect, useState } from "react";
import { type CoinData } from "../types/types";
import axios from "axios";
import StatusMessage from "../ui/StatusMessage";
import { ArrowUp, ArrowDown } from "lucide-react";
import { useParams } from "react-router";

const CoinDetailsPage = () => {
    const { id } = useParams();
    const [coin, setCoin] = useState<CoinData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        setLoading(true);
        axios
            .get(
                `${
                    import.meta.env.VITE_API_BASE_URL || "http://localhost:4000"
                }/api/coingecko/coins/${id}`,
                {}
            )
            .then((res) => {
                setCoin(res.data);
                console.log(res);
                console.log(res.data);
                console.log(res.data.id);
            })
            .catch((err) => {
                console.error(err);
                setError("Failed to fetch coin details.");
            })
            .finally(() => setLoading(false));
    }, [id]);

    if (loading)
        return <StatusMessage type="loading" message="Loading coin data..." />;
    if (error || !coin)
        return (
            <StatusMessage type="404" message={error || "Coin not found."} />
        );

    const {
        name,
        symbol,
        image,
        current_price,
        market_cap,
        market_cap_rank,
        total_volume,
        price_change_percentage_24h,
        ath,
        ath_change_percentage,
        circulating_supply,
        max_supply,
    } = coin;
    console.log(market_cap_rank, symbol);

    const isUp = price_change_percentage_24h >= 0;

    return (
        <div className="max-w-5xl mx-auto px-4 py-10">
            {/* Header */}
            <div className="flex items-center gap-4 mb-8">
                <img src={image} alt={name} className="w-14 h-14" />
                <div>
                    <h1 className="text-2xl font-bold text-gray-800">
                        {name} ({symbol.toUpperCase()})
                    </h1>
                    <p className="text-sm text-gray-500">
                        Rank #{market_cap_rank}
                    </p>
                </div>
                <div className="ml-auto text-right">
                    <p className="text-xl font-semibold text-gray-800">
                        💰 ${current_price?.toLocaleString()}
                    </p>
                    <p
                        className={`text-sm font-medium flex items-center justify-end ${
                            isUp ? "text-green-600" : "text-red-600"
                        }`}
                    >
                        {isUp ? <ArrowUp size={14} /> : <ArrowDown size={14} />}
                        {price_change_percentage_24h?.toFixed(2)}% (24h)
                    </p>
                </div>
            </div>

            {/* Stats Grid */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-10">
                <Stat
                    label="Market Cap"
                    value={`$${market_cap?.toLocaleString()}`}
                />
                <Stat
                    label="Volume (24h)"
                    value={`$${total_volume?.toLocaleString()}`}
                />
                <Stat
                    label="Circulating Supply"
                    value={circulating_supply?.toLocaleString()}
                />
                <Stat
                    label="Max Supply"
                    value={max_supply ? max_supply.toLocaleString() : "∞"}
                />
            </div>

            {/* ATH Section */}
            <div className="grid md:grid-cols-2 gap-6 text-sm text-gray-600">
                <div className="bg-gray-50 rounded-xl p-4 shadow-sm">
                    <p>
                        🚀 All-Time High:{" "}
                        <span className="font-semibold">
                            ${ath?.toLocaleString()}
                        </span>
                    </p>
                    <p>
                        📉 Change Since ATH:{" "}
                        <span className="font-semibold">
                            {ath_change_percentage?.toFixed(2)}%
                        </span>
                    </p>
                </div>
                <div className="bg-gray-50 rounded-xl p-4 shadow-sm">
                    <p>
                        🪙 Circulating:{" "}
                        <span className="font-semibold">
                            {circulating_supply?.toLocaleString()}
                        </span>
                    </p>
                    <p>
                        🎯 Max Supply:{" "}
                        <span className="font-semibold">
                            {max_supply ? max_supply?.toLocaleString() : "∞"}
                        </span>
                    </p>
                </div>
            </div>
        </div>
    );
};

const Stat = ({ label, value }: { label: string; value: string }) => (
    <div className="bg-white border rounded-lg shadow-sm px-4 py-3">
        <p className="text-xs text-gray-500">{label}</p>
        <p className="text-base font-semibold text-gray-800">{value}</p>
    </div>
);

export default CoinDetailsPage;
