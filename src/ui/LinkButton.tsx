import { ExternalLink } from "lucide-react";

const LinkButton = ({ href, label }: { href: string; label: string }) => (
    <a
        href={href}
        target="_blank"
        rel="noopener noreferrer"
        className="flex items-center gap-2 bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg transition-colors"
    >
        <ExternalLink size={16} />
        {label}
    </a>
);

export default LinkButton;
