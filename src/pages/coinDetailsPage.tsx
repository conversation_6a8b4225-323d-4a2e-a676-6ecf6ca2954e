import { useEffect, useState } from "react";
import { type DetailedCoinData } from "../types/types";
import axios from "axios";
import StatusMessage from "../ui/StatusMessage";
import {
    ArrowUp,
    ArrowDown,
    Calendar,
    Users,
    Code,
    TrendingUp,
    Globe,
    Star,
} from "lucide-react";
import { useParams } from "react-router";

import MetricCard from "../components/coinDetails/MetricCard";
import PriceChangeCard from "../components/coinDetails/PriceChangeCard";
import LinkButton from "../ui/LinkButton";

const CoinDetailsPage = () => {
    const { id } = useParams();
    const [coin, setCoin] = useState<DetailedCoinData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        setLoading(true);
        axios
            .get(
                `${
                    import.meta.env.VITE_API_BASE_URL || "http://localhost:4000"
                }/api/coingecko/coins/${id}`,
                {}
            )
            .then((res) => {
                setCoin(res.data);
                console.log(res.data);
            })
            .catch((err) => {
                console.error(err);
                setError("Failed to fetch coin details.");
            })
            .finally(() => setLoading(false));
    }, [id]);

    if (loading)
        return <StatusMessage type="loading" message="Loading coin data..." />;
    if (error || !coin)
        return (
            <StatusMessage type="404" message={error || "Coin not found."} />
        );

    const marketData = coin.market_data;
    const currentPrice = marketData.current_price.usd;
    const priceChange24h = marketData.price_change_percentage_24h;
    const isUp = priceChange24h >= 0;

    return (
        <div className="max-w-7xl mx-auto px-4 py-8 space-y-8">
            {/* Header Section */}
            <div className="bg-white rounded-xl border-2 border-gray-300 border-b-5 p-6">
                <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
                    <div className="flex items-center gap-4">
                        <img
                            src={coin.image.large}
                            alt={coin.name}
                            className="w-16 h-16"
                        />
                        <div>
                            <h1 className="text-3xl font-bold text-gray-800">
                                {coin.name} ({coin.symbol.toUpperCase()})
                            </h1>
                            <div className="flex  items-center gap-4 mt-2">
                                <span className="bg-blue-100 font-extrabold text-blue-800 px-4 py-1 rounded-full text-sm">
                                    Rank #{coin.market_cap_rank}
                                </span>
                                {coin.genesis_date && (
                                    <div className=" text-gray-600 flex items-center gap-2">
                                        <span className=" gap-2 text-sm">
                                            <Calendar size={14} />
                                        </span>
                                        <span className="font-extralight">
                                            {new Date(
                                                coin.genesis_date
                                            ).toLocaleDateString()}
                                        </span>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    <div className="ml-auto">
                        <div className="text-right">
                            <p className="text-3xl font-bold text-gray-800">
                                ${currentPrice.toLocaleString()}
                            </p>
                            <div
                                className={`flex items-center justify-end gap-1 text-lg font-semibold ${
                                    isUp ? "text-green-600" : "text-red-600"
                                }`}
                            >
                                {isUp ? (
                                    <ArrowUp size={18} />
                                ) : (
                                    <ArrowDown size={18} />
                                )}
                                {priceChange24h.toFixed(2)}% (24h)
                            </div>
                        </div>
                    </div>
                </div>

                {/* Categories */}
                {coin.categories.length > 0 && (
                    <div className="mt-4">
                        <div className="flex flex-wrap gap-2">
                            {coin.categories
                                .slice(0, 6)
                                .map((category, index) => (
                                    <span
                                        key={index}
                                        className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm"
                                    >
                                        {category}
                                    </span>
                                ))}
                        </div>
                    </div>
                )}
            </div>

            {/* Technical Details */}
            <div className="bg-white p-6">
                <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                    <Code size={20} />
                    Technical Details
                </h2>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                        <p className="text-sm text-gray-600">
                            Hashing Algorithm
                        </p>
                        <p className="font-semibold">
                            {coin.hashing_algorithm}
                        </p>
                    </div>
                    <div>
                        <p className="text-sm text-gray-600">Block Time</p>
                        <p className="font-semibold">
                            {coin.block_time_in_minutes} minutes
                        </p>
                    </div>
                    <div>
                        <p className="text-sm text-gray-600">Market Cap Rank</p>
                        <p className="font-semibold">#{coin.market_cap_rank}</p>
                    </div>
                </div>
            </div>

            {/* Description */}
            {coin.description.en && (
                <div className="bg-white p-2">
                    <h2 className="text-xl font-bold text-gray-800 mb-4">
                        About {coin.name}
                    </h2>
                    <div
                        className="prose max-w-none text-gray-700"
                        dangerouslySetInnerHTML={{
                            __html: coin.description.en,
                        }}
                    />
                </div>
            )}
            {/* Key Metrics Grid */}
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                <MetricCard
                    label="Market Cap"
                    value={`$${marketData.market_cap.usd.toLocaleString()}`}
                    change={marketData.market_cap_change_percentage_24h}
                />
                <MetricCard
                    label="Volume (24h)"
                    value={`$${marketData.total_volume.usd.toLocaleString()}`}
                />
                <MetricCard
                    label="Circulating Supply"
                    value={marketData.circulating_supply.toLocaleString()}
                />
                <MetricCard
                    label="Total Supply"
                    value={marketData.total_supply.toLocaleString()}
                />
                <MetricCard
                    label="Max Supply"
                    value={
                        marketData.max_supply
                            ? marketData.max_supply.toLocaleString()
                            : "∞"
                    }
                />
                <MetricCard
                    label="FDV"
                    value={`$${marketData.fully_diluted_valuation.usd.toLocaleString()}`}
                />
            </div>

            {/* Price Performance */}
            <div className="bg-white rounded-xl shadow-lg p-6">
                <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                    <TrendingUp size={20} />
                    Price Performance
                </h2>
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    <PriceChangeCard
                        label="1h"
                        value={
                            marketData.price_change_percentage_1h_in_currency
                                ?.usd
                        }
                    />
                    <PriceChangeCard
                        label="24h"
                        value={marketData.price_change_percentage_24h}
                    />
                    <PriceChangeCard
                        label="7d"
                        value={marketData.price_change_percentage_7d}
                    />
                    <PriceChangeCard
                        label="30d"
                        value={marketData.price_change_percentage_30d}
                    />
                    <PriceChangeCard
                        label="60d"
                        value={marketData.price_change_percentage_60d}
                    />
                    <PriceChangeCard
                        label="1y"
                        value={marketData.price_change_percentage_1y}
                    />
                </div>
            </div>

            {/* ATH/ATL Section */}
            <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-white rounded-xl shadow-lg p-6">
                    <h3 className="text-lg font-bold text-gray-800 mb-4">
                        All-Time High
                    </h3>
                    <div className="space-y-3">
                        <div>
                            <p className="text-2xl font-bold text-green-600">
                                ${marketData.ath.usd.toLocaleString()}
                            </p>
                            <p className="text-sm text-gray-600">
                                {new Date(
                                    marketData.ath_date.usd
                                ).toLocaleDateString()}
                            </p>
                        </div>
                        <div
                            className={`text-sm font-medium ${
                                marketData.ath_change_percentage.usd >= 0
                                    ? "text-green-600"
                                    : "text-red-600"
                            }`}
                        >
                            {marketData.ath_change_percentage.usd.toFixed(2)}%
                            from ATH
                        </div>
                    </div>
                </div>

                <div className="bg-white rounded-xl shadow-lg p-6">
                    <h3 className="text-lg font-bold text-gray-800 mb-4">
                        All-Time Low
                    </h3>
                    <div className="space-y-3">
                        <div>
                            <p className="text-2xl font-bold text-red-600">
                                ${marketData.atl.usd.toLocaleString()}
                            </p>
                            <p className="text-sm text-gray-600">
                                {new Date(
                                    marketData.atl_date.usd
                                ).toLocaleDateString()}
                            </p>
                        </div>
                        <div
                            className={`text-sm font-medium ${
                                marketData.atl_change_percentage.usd >= 0
                                    ? "text-green-600"
                                    : "text-red-600"
                            }`}
                        >
                            {marketData.atl_change_percentage.usd.toLocaleString()}
                            % from ATL
                        </div>
                    </div>
                </div>
            </div>

            {/* Community & Development */}
            <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-white rounded-xl shadow-lg p-6">
                    <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
                        <Users size={18} />
                        Community
                    </h3>
                    <div className="space-y-3">
                        <div className="flex justify-between">
                            <span className="text-gray-600">
                                Watchlist Users
                            </span>
                            <span className="font-semibold">
                                {coin.watchlist_portfolio_users.toLocaleString()}
                            </span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-gray-600">
                                Reddit Subscribers
                            </span>
                            <span className="font-semibold">
                                {coin.community_data.reddit_subscribers.toLocaleString()}
                            </span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-gray-600">
                                Sentiment (Up)
                            </span>
                            <span className="font-semibold text-green-600">
                                {coin.sentiment_votes_up_percentage.toFixed(1)}%
                            </span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-gray-600">
                                Sentiment (Down)
                            </span>
                            <span className="font-semibold text-red-600">
                                {coin.sentiment_votes_down_percentage.toFixed(
                                    1
                                )}
                                %
                            </span>
                        </div>
                    </div>
                </div>

                <div className="bg-white rounded-xl shadow-lg p-6">
                    <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
                        <Star size={18} />
                        Development
                    </h3>
                    <div className="space-y-3">
                        <div className="flex justify-between">
                            <span className="text-gray-600">GitHub Stars</span>
                            <span className="font-semibold">
                                {coin.developer_data.stars.toLocaleString()}
                            </span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-gray-600">Forks</span>
                            <span className="font-semibold">
                                {coin.developer_data.forks.toLocaleString()}
                            </span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-gray-600">Contributors</span>
                            <span className="font-semibold">
                                {coin.developer_data.pull_request_contributors}
                            </span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-gray-600">Commits (4w)</span>
                            <span className="font-semibold">
                                {coin.developer_data.commit_count_4_weeks}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            {/* Top Markets */}
            <div className="bg-white  p-2">
                <h2 className="text-xl font-bold text-gray-800 mb-6">
                    Top Markets
                </h2>
                <div className="overflow-x-auto">
                    <table className="w-full">
                        <thead>
                            <tr className="text-gray-500 text-sm">
                                <th className="text-left py-3 font-medium">
                                    #
                                </th>
                                <th className="text-left py-3 font-medium">
                                    Exchange
                                </th>
                                <th className="text-left py-3 font-medium">
                                    Pair
                                </th>
                                <th className="text-right py-3 font-medium">
                                    Price
                                </th>
                                <th className="text-right py-3 font-medium">
                                    Spread
                                </th>
                                <th className="text-right py-3 font-medium">
                                    +2% Depth
                                </th>
                                <th className="text-right py-3 font-medium">
                                    -2% Depth
                                </th>
                                <th className="text-right py-3 font-medium">
                                    24h Volume
                                </th>
                                <th className="text-right py-3 font-medium">
                                    Volume %
                                </th>
                                <th className="text-center py-3 font-medium">
                                    Confidence
                                </th>
                                <th className="text-right py-3 font-medium">
                                    Updated
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {coin.tickers.slice(0, 10).map((ticker, index) => {
                                const totalVolume = coin.tickers.reduce(
                                    (sum, t) => sum + t.converted_volume.usd,
                                    0
                                );
                                const volumePercentage =
                                    (ticker.converted_volume.usd /
                                        totalVolume) *
                                    100;
                                const updatedTime = new Date(
                                    ticker.last_traded_at
                                );
                                const now = new Date();
                                const timeDiff = Math.floor(
                                    (now.getTime() - updatedTime.getTime()) /
                                        (1000 * 60)
                                );

                                return (
                                    <tr
                                        key={index}
                                        className="border-b border-gray-100 hover:bg-gray-50 transition-colors"
                                    >
                                        <td className="py-4 text-gray-600 font-medium">
                                            {index + 1}
                                        </td>
                                        <td className="py-4">
                                            <a
                                                href={ticker.trade_url}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="text-blue-600 hover:text-blue-800 font-medium transition-colors"
                                            >
                                                {ticker.market.name}
                                            </a>
                                        </td>
                                        <td className="py-4">
                                            <div className="flex items-center gap-2">
                                                <span className="font-medium text-gray-900">
                                                    {ticker.base}
                                                </span>
                                                <span className="text-gray-400">
                                                    /
                                                </span>
                                                <span className="text-gray-600">
                                                    {ticker.target}
                                                </span>
                                                <span className="bg-green-100 text-green-700 px-2 py-1 rounded text-xs font-medium">
                                                    Spot
                                                </span>
                                            </div>
                                        </td>
                                        <td className="py-4 text-right font-medium text-gray-900">
                                            $
                                            {ticker.converted_last.usd.toLocaleString(
                                                undefined,
                                                {
                                                    minimumFractionDigits: 2,
                                                    maximumFractionDigits: 2,
                                                }
                                            )}
                                        </td>
                                        <td className="py-4 text-right text-gray-600">
                                            {ticker.bid_ask_spread_percentage.toFixed(
                                                2
                                            )}
                                            %
                                        </td>
                                        <td className="py-4 text-right text-gray-600">
                                            $
                                            {(
                                                ticker.converted_volume.usd *
                                                0.02
                                            ).toLocaleString(undefined, {
                                                maximumFractionDigits: 0,
                                            })}
                                        </td>
                                        <td className="py-4 text-right text-gray-600">
                                            $
                                            {(
                                                ticker.converted_volume.usd *
                                                0.02
                                            ).toLocaleString(undefined, {
                                                maximumFractionDigits: 0,
                                            })}
                                        </td>
                                        <td className="py-4 text-right font-medium text-gray-900">
                                            $
                                            {ticker.converted_volume.usd.toLocaleString(
                                                undefined,
                                                {
                                                    maximumFractionDigits: 0,
                                                }
                                            )}
                                        </td>
                                        <td className="py-4 text-right text-gray-600">
                                            {volumePercentage.toFixed(2)}%
                                        </td>
                                        <td className="py-4 text-center">
                                            <div className="flex items-center justify-center">
                                                <div
                                                    className={`w-3 h-3 rounded-full ${
                                                        ticker.trust_score ===
                                                        "green"
                                                            ? "bg-green-500"
                                                            : ticker.trust_score ===
                                                              "yellow"
                                                            ? "bg-yellow-500"
                                                            : "bg-red-500"
                                                    }`}
                                                ></div>
                                            </div>
                                        </td>
                                        <td className="py-4 text-right text-gray-500 text-sm">
                                            {timeDiff < 60
                                                ? `${timeDiff}m ago`
                                                : timeDiff < 1440
                                                ? `${Math.floor(
                                                      timeDiff / 60
                                                  )}h ago`
                                                : `${Math.floor(
                                                      timeDiff / 1440
                                                  )}d ago`}
                                        </td>
                                    </tr>
                                );
                            })}
                        </tbody>
                    </table>
                </div>
            </div>

            {/* Links */}
            <div className="bg-white rounded-xl shadow-lg p-6">
                <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                    <Globe size={20} />
                    Links & Resources
                </h2>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {coin.links.homepage[0] && (
                        <LinkButton
                            href={coin.links.homepage[0]}
                            label="Website"
                        />
                    )}
                    {coin.links.whitepaper && (
                        <LinkButton
                            href={coin.links.whitepaper}
                            label="Whitepaper"
                        />
                    )}
                    {coin.links.repos_url.github[0] && (
                        <LinkButton
                            href={coin.links.repos_url.github[0]}
                            label="GitHub"
                        />
                    )}
                    {coin.links.subreddit_url && (
                        <LinkButton
                            href={coin.links.subreddit_url}
                            label="Reddit"
                        />
                    )}
                    {coin.links.twitter_screen_name && (
                        <LinkButton
                            href={`https://twitter.com/${coin.links.twitter_screen_name}`}
                            label="Twitter"
                        />
                    )}
                    {coin.links.blockchain_site[0] && (
                        <LinkButton
                            href={coin.links.blockchain_site[0]}
                            label="Explorer"
                        />
                    )}
                </div>
            </div>
        </div>
    );
};

export default CoinDetailsPage;
