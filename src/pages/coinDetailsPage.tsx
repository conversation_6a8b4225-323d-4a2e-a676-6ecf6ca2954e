import { useEffect, useState } from "react";
import { type DetailedCoinData } from "../types/types";
import axios from "axios";
import StatusMessage from "../ui/StatusMessage";
import {
    ArrowUp,
    ArrowDown,
    Calendar,
    Users,
    Code,
    TrendingUp,
    Globe,
    Star,
    Info,
} from "lucide-react";
import { useParams } from "react-router";

import MetricCard from "../components/coinDetails/MetricCard";
import PricePerformanceChart from "../components/coinDetails/PricePerformanceChart";

const CoinDetailsPage = () => {
    const { id } = useParams();
    const [coin, setCoin] = useState<DetailedCoinData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        setLoading(true);
        axios
            .get(
                `${
                    import.meta.env.VITE_API_BASE_URL || "http://localhost:4000"
                }/api/coingecko/coins/${id}`,
                {}
            )
            .then((res) => {
                setCoin(res.data);
                console.log(res.data);
            })
            .catch((err) => {
                console.error(err);
                setError("Failed to fetch coin details.");
            })
            .finally(() => setLoading(false));
    }, [id]);

    if (loading)
        return <StatusMessage type="loading" message="Loading coin data..." />;

    if (error || !coin)
        return (
            <StatusMessage type="404" message={error || "Coin not found."} />
        );

    const marketData = coin.market_data;
    const currentPrice = marketData.current_price.usd;
    const priceChange24h = marketData.price_change_percentage_24h;
    const isUp = priceChange24h >= 0;

    return (
        <div className="mx-auto px-2 py-2 space-y-2">
            {/* Header Section */}
            <div className="bg-white rounded-xl mb-10 border-2 border-gray-300 border-b-5 p-6">
                <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
                    <div className="flex items-center gap-4">
                        <img
                            src={coin.image.large}
                            alt={coin.name}
                            className="w-16 h-16"
                        />
                        <div>
                            <h1 className="text-3xl font-bold text-gray-800">
                                {coin.name} ({coin.symbol.toUpperCase()})
                            </h1>
                            <div className="flex  items-center gap-4 mt-2">
                                <span className="bg-blue-100 font-extrabold text-blue-800 px-4 py-1 rounded-full text-sm">
                                    Rank #{coin.market_cap_rank}
                                </span>
                                {coin.genesis_date && (
                                    <div className=" text-gray-600 flex items-center gap-2">
                                        <span className=" gap-2 text-sm">
                                            <Calendar size={14} />
                                        </span>
                                        <span className="font-extralight">
                                            {new Date(
                                                coin.genesis_date
                                            ).toLocaleDateString()}
                                        </span>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    <div className="ml-auto">
                        <div className="text-right">
                            <p className="text-3xl font-bold text-gray-800">
                                ${currentPrice.toLocaleString()}
                            </p>
                            <div
                                className={`flex items-center justify-end gap-1 text-lg font-semibold ${
                                    isUp ? "text-green-600" : "text-red-600"
                                }`}
                            >
                                {isUp ? (
                                    <ArrowUp size={18} />
                                ) : (
                                    <ArrowDown size={18} />
                                )}
                                {priceChange24h.toFixed(2)}% (24h)
                            </div>
                        </div>
                    </div>
                </div>

                {/* Categories */}
                {coin.categories.length > 0 && (
                    <div className="mt-4">
                        <div className="flex flex-wrap gap-2">
                            {coin.categories
                                .slice(0, 6)
                                .map((category, index) => (
                                    <span
                                        key={index}
                                        className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm"
                                    >
                                        {category}
                                    </span>
                                ))}
                        </div>
                    </div>
                )}
            </div>

            <div className="flex">
                <div className="flex-2">
                    {/* Technical Details */}
                    <div className="bg-white p-2 mb-4">
                        <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                            <Code size={20} />
                            Technical Details
                        </h2>
                        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <div>
                                <p className="text-sm text-gray-600">
                                    Hashing Algorithm
                                </p>
                                <p className="font-semibold">
                                    {coin.hashing_algorithm}
                                </p>
                            </div>
                            <div>
                                <p className="text-sm text-gray-600">
                                    Block Time
                                </p>
                                <p className="font-semibold">
                                    {coin.block_time_in_minutes} minutes
                                </p>
                            </div>
                            <div>
                                <p className="text-sm text-gray-600">
                                    Market Cap Rank
                                </p>
                                <p className="font-semibold">
                                    #{coin.market_cap_rank}
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Description */}
                    {coin.description.en && (
                        <div className="bg-white p-2">
                            <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                                <Info size={20} />
                                About {coin.name}
                            </h2>
                            <div
                                className="prose max-w-none text-gray-700"
                                dangerouslySetInnerHTML={{
                                    __html: coin.description.en,
                                }}
                            />
                        </div>
                    )}
                </div>

                <div className="flex-1">
                    {/* Price Performance Chart */}
                    <PricePerformanceChart marketData={marketData} />
                </div>
            </div>

            {/* ATH/ATL Section */}
            <div className="grid md:grid-cols-3 gap-6">
                <div className="flex-1 bg-white border-2 border-b-5 flex justify-between border-gray-300 rounded-xl p-6">
                    <div>
                        <h3 className="text-lg font-bold text-gray-800 mb-4">
                            All-Time High
                        </h3>
                        <div className="space-y-3">
                            <div>
                                <p className="text-2xl font-bold text-green-600">
                                    ${marketData.ath.usd.toLocaleString()}
                                </p>
                                <p className="text-sm text-gray-600">
                                    {new Date(
                                        marketData.ath_date.usd
                                    ).toLocaleDateString()}
                                </p>
                            </div>
                            <div
                                className={`text-sm font-medium ${
                                    marketData.ath_change_percentage.usd >= 0
                                        ? "text-green-600"
                                        : "text-red-600"
                                }`}
                            >
                                {marketData.ath_change_percentage.usd.toFixed(
                                    2
                                )}
                                % from ATH
                            </div>
                        </div>
                    </div>
                    <div>
                        <h3 className="text-lg font-bold text-gray-800 mb-4">
                            All-Time Low
                        </h3>
                        <div className="space-y-3">
                            <div>
                                <p className="text-2xl font-bold text-red-600">
                                    ${marketData.atl.usd.toLocaleString()}
                                </p>
                                <p className="text-sm text-gray-600">
                                    {new Date(
                                        marketData.atl_date.usd
                                    ).toLocaleDateString()}
                                </p>
                            </div>
                            <div
                                className={`text-sm font-medium ${
                                    marketData.atl_change_percentage.usd >= 0
                                        ? "text-green-600"
                                        : "text-red-600"
                                }`}
                            >
                                {marketData.atl_change_percentage.usd.toLocaleString()}
                                % from ATL
                            </div>
                        </div>
                    </div>
                </div>
                {/* Key Metrics Grid */}
                <div className="grid grid-cols-3 col-span-2  md:grid-cols-4 lg:grid-cols-4  gap-4">
                    <MetricCard
                        label="Market Cap"
                        value={`$${marketData.market_cap.usd.toLocaleString()}`}
                        change={marketData.market_cap_change_percentage_24h}
                    />
                    <MetricCard
                        label="Volume (24h)"
                        value={`$${marketData.total_volume.usd.toLocaleString()}`}
                    />
                    <MetricCard
                        label="Circulating Supply"
                        value={marketData.circulating_supply.toLocaleString()}
                    />
                    <MetricCard
                        label="Total Supply"
                        value={marketData.total_supply.toLocaleString()}
                    />
                    <MetricCard
                        label="Max Supply"
                        value={
                            marketData.max_supply
                                ? marketData.max_supply.toLocaleString()
                                : "∞"
                        }
                    />
                    <MetricCard
                        label="FDV"
                        value={`$${marketData.fully_diluted_valuation.usd.toLocaleString()}`}
                    />
                </div>
            </div>

            {/* Top Markets */}
            <div className="bg-white mt-10 p-2">
                <h2 className="text-xl font-bold text-gray-800 mb-6">
                    Top Markets
                </h2>
                <div className="overflow-x-auto">
                    <table className="w-full">
                        <thead>
                            <tr className="text-gray-500 text-sm font-bold">
                                <th className="text-left py-3">#</th>
                                <th className="text-left py-3">Exchange</th>
                                <th className="text-left py-3">Pair</th>
                                <th className="text-right py-3">Price</th>
                                <th className="text-right py-3">Spread</th>
                                <th className="text-right py-3">+2% Depth</th>
                                <th className="text-right py-3">-2% Depth</th>
                                <th className="text-right py-3">24h Volume</th>
                                <th className="text-right py-3">Volume %</th>
                                <th className="text-center py-3">Confidence</th>
                                <th className="text-right py-3 ">Updated</th>
                            </tr>
                        </thead>
                        <tbody>
                            {coin.tickers.slice(0, 10).map((ticker, index) => {
                                const totalVolume = coin.tickers.reduce(
                                    (sum, t) => sum + t.converted_volume.usd,
                                    0
                                );
                                const volumePercentage =
                                    (ticker.converted_volume.usd /
                                        totalVolume) *
                                    100;
                                const updatedTime = new Date(
                                    ticker.last_traded_at
                                );
                                const now = new Date();
                                const timeDiff = Math.floor(
                                    (now.getTime() - updatedTime.getTime()) /
                                        (1000 * 60)
                                );

                                return (
                                    <tr
                                        key={index}
                                        className="border-b border-gray-100 hover:bg-gray-50 transition-colors"
                                    >
                                        <td className="py-4 text-gray-600 font-medium">
                                            {index + 1}
                                        </td>
                                        <td className="py-4">
                                            <a
                                                href={ticker.trade_url}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="text-blue-600 hover:text-blue-800 font-medium transition-colors"
                                            >
                                                {ticker.market.name}
                                            </a>
                                        </td>
                                        <td className="py-4">
                                            <div className="flex items-center gap-2">
                                                <span className="font-medium text-gray-900">
                                                    {ticker.base}
                                                </span>
                                                <span className="text-gray-400">
                                                    /
                                                </span>
                                                <span className="text-gray-600">
                                                    {ticker.target}
                                                </span>
                                                <span className="bg-green-100 text-green-700 px-2 py-1 rounded text-xs font-medium">
                                                    Spot
                                                </span>
                                            </div>
                                        </td>
                                        <td className="py-4 text-right font-medium text-gray-900">
                                            $
                                            {ticker.converted_last.usd.toLocaleString(
                                                undefined,
                                                {
                                                    minimumFractionDigits: 2,
                                                    maximumFractionDigits: 2,
                                                }
                                            )}
                                        </td>
                                        <td className="py-4 text-right text-gray-600">
                                            {ticker.bid_ask_spread_percentage.toFixed(
                                                2
                                            )}
                                            %
                                        </td>
                                        <td className="py-4 text-right text-gray-600">
                                            $
                                            {(
                                                ticker.converted_volume.usd *
                                                0.02
                                            ).toLocaleString(undefined, {
                                                maximumFractionDigits: 0,
                                            })}
                                        </td>
                                        <td className="py-4 text-right text-gray-600">
                                            $
                                            {(
                                                ticker.converted_volume.usd *
                                                0.02
                                            ).toLocaleString(undefined, {
                                                maximumFractionDigits: 0,
                                            })}
                                        </td>
                                        <td className="py-4 text-right font-medium text-gray-900">
                                            $
                                            {ticker.converted_volume.usd.toLocaleString(
                                                undefined,
                                                {
                                                    maximumFractionDigits: 0,
                                                }
                                            )}
                                        </td>
                                        <td className="py-4 text-right text-gray-600">
                                            {volumePercentage.toFixed(2)}%
                                        </td>
                                        <td className="py-4 text-center">
                                            <div className="flex items-center justify-center">
                                                <div
                                                    className={`w-3 h-3 rounded-full ${
                                                        ticker.trust_score ===
                                                        "green"
                                                            ? "bg-green-500"
                                                            : ticker.trust_score ===
                                                              "yellow"
                                                            ? "bg-yellow-500"
                                                            : "bg-red-500"
                                                    }`}
                                                ></div>
                                            </div>
                                        </td>
                                        <td className="py-4 text-right text-gray-500 text-sm">
                                            {timeDiff < 60
                                                ? `${timeDiff}m ago`
                                                : timeDiff < 1440
                                                ? `${Math.floor(
                                                      timeDiff / 60
                                                  )}h ago`
                                                : `${Math.floor(
                                                      timeDiff / 1440
                                                  )}d ago`}
                                        </td>
                                    </tr>
                                );
                            })}
                        </tbody>
                    </table>
                </div>
            </div>

            {/* Community & Development */}
            <div className="grid md:grid-cols-2 gap-6  mt-10">
                <div className="bg-white p-6">
                    <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
                        <Users size={18} />
                        Community
                    </h3>
                    <div className="space-y-3">
                        <div className="flex justify-between">
                            <span className="text-gray-600">
                                Watchlist Users
                            </span>
                            <span className="font-semibold">
                                {coin.watchlist_portfolio_users.toLocaleString()}
                            </span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-gray-600">
                                Reddit Subscribers
                            </span>
                            <span className="font-semibold">
                                {coin.community_data.reddit_subscribers.toLocaleString()}
                            </span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-gray-600">
                                Sentiment (Up)
                            </span>
                            <span className="font-semibold text-green-600">
                                {coin.sentiment_votes_up_percentage.toFixed(1)}%
                            </span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-gray-600">
                                Sentiment (Down)
                            </span>
                            <span className="font-semibold text-red-600">
                                {coin.sentiment_votes_down_percentage.toFixed(
                                    1
                                )}
                                %
                            </span>
                        </div>
                    </div>
                </div>

                <div className="bg-white p-6">
                    <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
                        <Star size={18} />
                        Development
                    </h3>
                    <div className="space-y-3">
                        <div className="flex justify-between">
                            <span className="text-gray-600">GitHub Stars</span>
                            <span className="font-semibold">
                                {coin.developer_data.stars.toLocaleString()}
                            </span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-gray-600">Forks</span>
                            <span className="font-semibold">
                                {coin.developer_data.forks.toLocaleString()}
                            </span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-gray-600">Contributors</span>
                            <span className="font-semibold">
                                {coin.developer_data.pull_request_contributors}
                            </span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-gray-600">Commits (4w)</span>
                            <span className="font-semibold">
                                {coin.developer_data.commit_count_4_weeks}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            {/* Links */}
            <div className="bg-white p-2  mt-10">
                <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-2">
                    <Globe size={20} />
                    Links & Resources
                </h2>
                <div className="flex flex-wrap gap-4 items-center">
                    {coin.links.homepage[0] && (
                        <a
                            href={coin.links.homepage[0]}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center gap-3 text-gray-700 hover:text-blue-600 transition-colors"
                        >
                            <Globe size={18} className="text-gray-500" />
                            <span>Website</span>
                        </a>
                    )}
                    {coin.links.whitepaper && (
                        <a
                            href={coin.links.whitepaper}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center gap-3 text-gray-700 hover:text-blue-600 transition-colors"
                        >
                            <Info size={18} className="text-gray-500" />
                            <span>Whitepaper</span>
                        </a>
                    )}
                    {coin.links.repos_url.github[0] && (
                        <a
                            href={coin.links.repos_url.github[0]}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center gap-3 text-gray-700 hover:text-blue-600 transition-colors"
                        >
                            <Code size={18} className="text-gray-500" />
                            <span>GitHub Repository</span>
                        </a>
                    )}
                    {coin.links.subreddit_url && (
                        <a
                            href={coin.links.subreddit_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center gap-3 text-gray-700 hover:text-blue-600 transition-colors"
                        >
                            <Users size={18} className="text-gray-500" />
                            <span>Reddit Community</span>
                        </a>
                    )}
                    {coin.links.twitter_screen_name && (
                        <a
                            href={`https://twitter.com/${coin.links.twitter_screen_name}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center gap-3 text-gray-700 hover:text-blue-600 transition-colors"
                        >
                            <svg
                                className="w-[18px] h-[18px] text-gray-500"
                                fill="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" />
                            </svg>
                            <span>Twitter</span>
                        </a>
                    )}
                    {coin.links.blockchain_site[0] && (
                        <a
                            href={coin.links.blockchain_site[0]}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center gap-3 text-gray-700 hover:text-blue-600 transition-colors"
                        >
                            <TrendingUp size={18} className="text-gray-500" />
                            <span>Blockchain Explorer</span>
                        </a>
                    )}
                    {coin.links.official_forum_url[0] && (
                        <a
                            href={coin.links.official_forum_url[0]}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center gap-3 text-gray-700 hover:text-blue-600 transition-colors"
                        >
                            <Users size={18} className="text-gray-500" />
                            <span>Official Forum</span>
                        </a>
                    )}
                </div>
            </div>
        </div>
    );
};

export default CoinDetailsPage;
